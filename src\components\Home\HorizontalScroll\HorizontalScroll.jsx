import React, { useEffect } from 'react';
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import './HorizontalScroll.css';

const HorizontalScroll = () => {
    useEffect(() => {
        gsap.registerPlugin(ScrollTrigger);

        let horizontalScrollTrigger = null;

        const initAnimations = () => {
            if (horizontalScrollTrigger) {
                horizontalScrollTrigger.kill();
            }

            // Horizontal scroll animation
            horizontalScrollTrigger = ScrollTrigger.create({
                trigger: ".horizontal-scroll-container",
                start: "top top",
                end: "bottom bottom",
                scrub: 1,
                pin: ".horizontal-scroll-wrapper",
                animation: gsap.to(".horizontal-scroll-wrapper", {
                    x: "-100vw", // Move 1 viewport width to show both items
                    ease: "none",
                }),
                onEnter: () => {
                    // Hide navbar when entering horizontal scroll section
                    const navbar = document.querySelector('.navbar');
                    if (navbar) {
                        navbar.classList.add('navbar-horizontal-hidden');
                    }
                },
                onLeave: () => {
                    // Show navbar when leaving horizontal scroll section
                    const navbar = document.querySelector('.navbar');
                    if (navbar) {
                        navbar.classList.remove('navbar-horizontal-hidden');
                    }
                },
                onEnterBack: () => {
                    // Hide navbar when entering back into horizontal scroll section
                    const navbar = document.querySelector('.navbar');
                    if (navbar) {
                        navbar.classList.add('navbar-horizontal-hidden');
                    }
                },
                onLeaveBack: () => {
                    // Show navbar when leaving back from horizontal scroll section
                    const navbar = document.querySelector('.navbar');
                    if (navbar) {
                        navbar.classList.remove('navbar-horizontal-hidden');
                    }
                }
            });
        };

        initAnimations();

        const handleResize = () => {
            initAnimations();
        };

        window.addEventListener("resize", handleResize);

        // Cleanup function
        return () => {
            if (horizontalScrollTrigger) {
                horizontalScrollTrigger.kill();
            }
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    return (
        <>
            {/* horizontal scroll section */}
            <section className="horizontal-scroll-container">
                <div className="horizontal-scroll-wrapper">
                    <section className="services">
                        <div className="services-col">
                            <div className="services-banner">
                                <img src="./Horizontal_scroll/img1.png" alt="Healthcare Transformation" />
                            </div>
                            <p className="primary">Healthcare Transformation</p>
                        </div>
                        <div className="services-col">
                            <h4>
                                Manual workflows, endless paperwork, frustrated patients, overwhelmed staff.
                                This is healthcare today. But it doesn't have to be tomorrow.
                                AIR-HS transforms chaos into harmony through intelligent automation.
                            </h4>

                            <div className="services-list">
                                <div className="service-list-row">
                                    <div className="service-list-col">
                                        <h5>Current Problems</h5>
                                    </div>
                                    <div className="service-list-col">
                                        <p>
                                            Long patient wait times, paper-based errors, burdened receptionists,
                                            excessive doctor paperwork, and rising operational costs plague healthcare today.
                                        </p>
                                    </div>
                                </div>

                                <div className="service-list-row">
                                    <div className="service-list-col">
                                        <h5>Our Solution</h5>
                                    </div>
                                    <div className="service-list-col">
                                        <p>
                                            Complete hospital automation that reduces operational costs by 30%,
                                            eliminates human errors, and makes healthcare 15-20% more affordable
                                            while improving doctor quality of life.
                                        </p>
                                    </div>
                                </div>

                                <div className="service-list-row">
                                    <div className="service-list-col">
                                    </div>
                                    <div className="service-list-col">
                                        <div className="service-buttons">
                                            <button className="service-btn">See How It Works</button>
                                            <button className="service-btn">Talk to Our Team</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section className="services">
                        <div className="services-col">
                            <div className="services-banner">
                                <img src="/healthcare/pricing-banner.jpg" alt="Pricing - Affordable AI Automation" />
                            </div>
                            <p className="primary">PRICING</p>
                        </div>
                        <div className="services-col">
                            <h4>
                                Affordable AI Automation for Healthcare. At AIR-HS, we believe in making cutting-edge AI automation
                                accessible to hospitals, clinics, labs, and pharmacies across India.
                            </h4>

                            <div className="services-list">
                                <div className="service-list-row">
                                    <div className="service-list-col">
                                        <h5>Free Pilot Program</h5>
                                    </div>
                                    <div className="service-list-col">
                                        <p>
                                            Discover how AIR-HS transforms your healthcare operations with our no-cost pilot program.
                                            30-day trial tailored to your hospital or clinic's needs.
                                        </p>
                                    </div>
                                </div>

                                <div className="service-list-row">
                                    <div className="service-list-col">
                                        <h5>What's Included</h5>
                                    </div>
                                    <div className="service-list-col">
                                        <p>
                                            Full access to core modules: AI Calling Agents, Pen-Paper EMR, Queue Management,
                                            seamless integration, dedicated support, and real-time analytics dashboard.
                                        </p>
                                    </div>
                                </div>

                                <div className="service-list-row">
                                    <div className="service-list-col">
                                        <h5>Why Try the Pilot?</h5>
                                    </div>
                                    <div className="service-list-col">
                                        <p>
                                            Reduce patient wait times, evaluate cost savings, validate AIR-HS impact
                                            with your team. No obligation - experience benefits risk-free.
                                        </p>
                                    </div>
                                </div>

                                <div className="service-list-row">
                                    <div className="service-list-col">
                                    </div>
                                    <div className="service-list-col">
                                        <div className="service-buttons">
                                            <button className="service-btn">Start Free Trial</button>
                                            <button className="service-btn">Request Demo</button>
                                        </div>
                                        <p style={{ marginTop: '1rem', fontSize: '0.9rem', fontStyle: 'italic', opacity: '0.8' }}>
                                            Further subscription plans will announce soon
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </section>
        </>
    );
};

export default HorizontalScroll;

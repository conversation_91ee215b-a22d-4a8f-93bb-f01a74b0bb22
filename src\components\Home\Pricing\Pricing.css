/* Pricing Section */
.pricing-section {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  padding: 4rem 2rem;
  background-image: url('/Bg/image.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pricing-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(18, 18, 20, 0.85);
  z-index: 1;
}

.pricing-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* Header Section */
.pricing-header {
  text-align: center;
  color: #f7f7f2;
}

.pricing-title h1 {
  font-family: 'Rader', sans-serif;
  font-size: clamp(3rem, 6vw, 6rem);
  font-weight: 700;
  font-style: italic;
  letter-spacing: 0.1em;
  margin: 0 0 1rem 0;
  color: #f7f7f2;
}

.pricing-subtitle p {
  font-family: 'Rader', sans-serif;
  font-size: clamp(1.2rem, 2vw, 1.8rem);
  color: #727072;
  margin: 0 0 2rem 0;
  font-weight: 500;
}

.pricing-description p {
  font-family: 'Rader', sans-serif;
  font-size: clamp(1rem, 1.2vw, 1.1rem);
  color: #f7f7f2;
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
  max-width: 800px;
  margin: 0 auto;
}

/* Pricing Card */
.pricing-card {
  background: linear-gradient(135deg, rgba(18, 18, 20, 0.9), rgba(114, 112, 114, 0.1));
  border: 1px dashed #727072;
  border-radius: 20px;
  padding: 3rem;
  backdrop-filter: blur(10px);
  color: #f7f7f2;
}

.pricing-card-header {
  text-align: center;
  margin-bottom: 3rem;
}

.card-title h2 {
  font-family: 'Rader', sans-serif;
  font-size: clamp(2rem, 3vw, 3rem);
  font-weight: 600;
  font-style: italic;
  color: #f7f7f2;
  margin: 0 0 1rem 0;
}

.card-subtitle p {
  font-family: 'Rader', sans-serif;
  font-size: clamp(1rem, 1.2vw, 1.1rem);
  color: #f7f7f2;
  line-height: 1.5;
  margin: 0;
  opacity: 0.9;
}

/* Pricing Card Content */
.pricing-card-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.features-title h3,
.benefits-title h3 {
  font-family: 'Rader', sans-serif;
  font-size: clamp(1.3rem, 1.8vw, 1.6rem);
  font-weight: 600;
  color: #f7f7f2;
  margin: 0 0 1.5rem 0;
}

.features-list,
.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feature-item p,
.benefit-item p {
  font-family: 'Rader', sans-serif;
  font-size: clamp(0.9rem, 1vw, 1rem);
  color: #f7f7f2;
  line-height: 1.4;
  margin: 0;
  padding-left: 1rem;
  position: relative;
}

.feature-item p::before,
.benefit-item p::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #727072;
  font-weight: bold;
}

/* Pricing Details */
.pricing-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.detail-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.detail-label {
  flex: 1;
  min-width: 100px;
}

.detail-label .caps {
  font-family: 'Rader', sans-serif;
  font-size: 0.9rem;
  color: #727072;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.detail-description {
  flex: 2;
}

.detail-description p {
  font-family: 'Rader', sans-serif;
  font-size: clamp(0.9rem, 1vw, 1rem);
  color: #f7f7f2;
  line-height: 1.4;
  margin: 0;
}

/* Benefits Section */
.pricing-benefits {
  margin-bottom: 2rem;
}

/* Footer */
.pricing-footer {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px dashed #727072;
}

.coming-soon {
  font-family: 'Rader', sans-serif;
  font-size: clamp(1rem, 1.2vw, 1.1rem);
  color: #727072;
  font-style: italic;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1000px) {
  .pricing-card-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .pricing-card {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .pricing-section {
    padding: 2rem 1rem;
  }
  
  .pricing-container {
    gap: 2rem;
  }
  
  .pricing-card {
    padding: 1.5rem;
  }
  
  .pricing-card-content {
    gap: 1.5rem;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .pricing-card {
    padding: 1rem;
  }
  
  .feature-item p,
  .benefit-item p {
    font-size: clamp(0.85rem, 1vw, 0.95rem);
  }
  
  .detail-description p {
    font-size: clamp(0.85rem, 1vw, 0.95rem);
  }
}

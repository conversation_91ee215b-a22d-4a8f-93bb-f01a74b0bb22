import React, { useRef } from 'react';
import Copy from '../../Copy/Copy';
import './Pricing.css';

const Pricing = () => {
  const containerRef = useRef(null);

  return (
    <div className="pricing-section" ref={containerRef}>
      <div className="pricing-container">
        {/* Header Section */}
        <div className="pricing-header">
          <Copy className="pricing-title">
            <h1>PRICING</h1>
          </Copy>
          <Copy className="pricing-subtitle">
            <p>Affordable AI Automation for Healthcare</p>
          </Copy>
          <Copy className="pricing-description">
            <p>
              At AIR-HS, we believe in making cutting-edge AI automation accessible to hospitals, 
              clinics, labs, and pharmacies across India. Our pricing model is designed to deliver 
              maximum value, reduce operational costs by 10-20%, and support scalable growth. 
              Start with our free pilot program to experience the power of AIR-HS firsthand, 
              with no upfront commitment.
            </p>
          </Copy>
        </div>

        {/* Pricing Card */}
        <div className="pricing-card">
          <div className="pricing-card-header">
            <Copy className="card-title">
              <h2>Free Pilot Program</h2>
            </Copy>
            <Copy className="card-subtitle">
              <p>
                Discover how AIR-HS transforms your healthcare operations with our no-cost pilot program. 
                Test our AI-driven solutions in your facility and see measurable improvements in efficiency, 
                patient care, and cost savings.
              </p>
            </Copy>
          </div>

          <div className="pricing-card-content">
            <div className="pricing-features">
              <Copy className="features-title">
                <h3>What's Included:</h3>
              </Copy>
              <div className="features-list">
                <Copy className="feature-item">
                  <p>Full access to core modules: AI Calling Agents, Pen-Paper EMR, and Queue Management.</p>
                </Copy>
                <Copy className="feature-item">
                  <p>Seamless integration with your existing workflows.</p>
                </Copy>
                <Copy className="feature-item">
                  <p>Dedicated support for setup and training.</p>
                </Copy>
                <Copy className="feature-item">
                  <p>Real-time analytics via the Central Intelligence Dashboard.</p>
                </Copy>
              </div>
            </div>

            <div className="pricing-details">
              <Copy className="detail-item">
                <div className="detail-label">
                  <span className="caps">Duration</span>
                </div>
                <div className="detail-description">
                  <p>30-day trial tailored to your hospital or clinic's needs.</p>
                </div>
              </Copy>
              
              <Copy className="detail-item">
                <div className="detail-label">
                  <span className="caps">No Obligation</span>
                </div>
                <div className="detail-description">
                  <p>Experience the benefits risk-free before committing.</p>
                </div>
              </Copy>
            </div>
          </div>

          <div className="pricing-benefits">
            <Copy className="benefits-title">
              <h3>Why Try the Pilot?</h3>
            </Copy>
            <div className="benefits-list">
              <Copy className="benefit-item">
                <p>Reduce patient wait times and administrative workload.</p>
              </Copy>
              <Copy className="benefit-item">
                <p>Evaluate cost savings and operational improvements.</p>
              </Copy>
              <Copy className="benefit-item">
                <p>Validate AIR-HS's impact with your team and patients.</p>
              </Copy>
            </div>
          </div>

          <div className="pricing-footer">
            <Copy className="footer-note">
              <p className="coming-soon">Further Subscription plans will announce soon</p>
            </Copy>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pricing;

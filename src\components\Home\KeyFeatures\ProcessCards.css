/* Key Features Header Section */
.key-features-header {

  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #f7f7f2;
  /* Maintain background transition from ProblemSol */

}

.key-features-container {
  border: dashed 1px #1a1a1a;
  width: 95%;
  height: 90%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50px;
  position: relative;
}

.key-features-header-content {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  z-index: 3;
}

.key-features-header-content .primary.sm {
  font-family: 'Rader', sans-serif;
  font-size: 0.9rem;
  color: #242424;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.key-features-header-content2 {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  z-index: 3;
}

.key-features-header-content2 .primary.sm {
  font-family: 'Rader', sans-serif;
  font-size: 0.9rem;
  color: #242424;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

/* Arrow line connecting header content elements */
.key-features-header-content::after {
  content: '';
  position: absolute;
  top: 50%;
  left: calc(100% + 20px);
  width: calc(100vw - 95% - 370px);
  height: 2px;
  background-color: #242424;

  transform: translateY(-50%);
  z-index: 2;
}

.key-features-header-content::before {
  content: '';
  position: absolute;
  top: 50%;
  left: calc(100vw - 95% - 190px);
  width: 0;
  height: 0;
  border-left: 15px solid #242424;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  transform: translateY(-50%);
  z-index: 2;
}

.key-features-footer-content {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  z-index: 3;
}

.key-features-footer-content .primary.sm {
  font-family: 'Rader', sans-serif;
  font-size: 0.9rem;
  color: #242424;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.key-features-footer-content2 {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  z-index: 3;
}

.key-features-footer-content2 .primary.sm {
  font-family: 'Rader', sans-serif;
  font-size: 0.9rem;
  color: #242424;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

/* Arrow line connecting footer content elements */
.key-features-footer-content::after {
  content: '';
  position: absolute;
  top: 50%;
  left: calc(100% + 20px);
  width: calc(100vw - 95% - 370px);
  height: 2px;
  background-color: #242424;

  transform: translateY(-50%);
  z-index: 2;
}

.key-features-footer-content::before {
  content: '';
  position: absolute;
  top: 50%;
  left: calc(100vw - 95% - 170px);
  width: 0;
  height: 0;
  border-left: 15px solid #242424;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  transform: translateY(-50%);
  z-index: 2;
}



.key-features-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;

  z-index: 1;
}

.key-features-header h1 {
  position: relative;
  z-index: 2;
  font-size: 8rem;
  font-weight: 700;
  letter-spacing: 0.1em;
  margin: 0;
  font-family: 'Rader', sans-serif;
  font-style: italic;
  color: #242424;
  /* Light gold/champagne color */
}

.process-cards {
  position: relative;
  width: 100%;
  height: 100%;
  /* Transition from ProblemSol background to main background image */
}

.process-card {
  position: relative;
  width: 100vw;
  height: 100svh;
  padding: 1.5rem;
  display: flex;
  gap: 3rem;
  border-radius: 50px 50px 0 0;
  will-change: transform;
  /* Add black overlay for better text visibility */
  background-image: linear-gradient(#1a2f47, rgba(18, 18, 20, 0.666));
  /* background-image: linear-gradient(#7e97bd, rgba(5, 28, 78, 0.85)); */

  backdrop-filter: blur(100px);
}

.process-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.1s ease;
  pointer-events: none;
  z-index: 2;
}

.process-card-index {
  flex: 2;
  color: #f7f7f2;
}

.process-card-content {
  flex: 4;
  padding-top: 1.5rem;
  color: #f7f7f2;
}

.process-card-content-wrapper {
  width: 85%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.process-card-header {
  width: 100%;
  font-size: clamp(1.8rem, 3vw, 2.5rem);
  margin-bottom: 0.5rem;
}

.process-card-subtitle {
  margin-bottom: 1rem;
}

.process-card-subtitle p {
  font-family: 'Rader', sans-serif;
  font-size: clamp(0.9rem, 1.2vw, 1.1rem);
  color: #f7f7f2;
  line-height: 1.4;
  opacity: 0.9;
}

.process-card-img {
  width: 100%;
  max-width: 400px;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.process-card-img img,
.process-card-img video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.process-card-features {
  margin: 1rem 0;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.feature-item p {
  font-family: 'Rader', sans-serif;
  font-size: clamp(0.8rem, 1vw, 0.95rem);
  color: #f7f7f2;
  line-height: 1.4;
  margin: 0;
  padding-left: 0.5rem;
}

.process-card-impact {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed rgba(247, 247, 242, 0.3);
}

.impact-label {
  flex: 1;
  min-width: 80px;
}

.impact-label .caps {
  font-family: 'Rader', sans-serif;
  font-size: 0.8rem;
  color: #f7f7f2;
  opacity: 0.7;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.impact-description {
  flex: 3;
}

.impact-description p {
  font-family: 'Rader', sans-serif;
  font-size: clamp(0.85rem, 1.1vw, 1rem);
  color: #f7f7f2;
  line-height: 1.4;
  margin: 0;
  font-weight: 600;
}

@media (max-width: 1000px) {
  .key-features-header h1 {
    font-size: 4rem;
  }

  .process-card {
    gap: 1rem;
    flex-direction: column;
    padding: 1rem;
  }

  .process-card-index {
    flex: none;
    text-align: center;
  }

  .process-card-content-wrapper {
    width: 100%;
    gap: 0.8rem;
  }

  .process-card-header {
    font-size: clamp(1.5rem, 2.5vw, 2rem);
  }

  .process-card-img {
    max-width: 350px;
    height: 180px;
  }

  .process-card-impact {
    flex-direction: column;
    gap: 0.5rem;
  }

  .feature-item p {
    font-size: clamp(0.75rem, 0.9vw, 0.85rem);
  }

  .impact-description p {
    font-size: clamp(0.8rem, 1vw, 0.9rem);
  }
}

@media (max-width: 768px) {
  .key-features-header h1 {
    font-size: 3rem;
    letter-spacing: 0.05em;
  }

  .key-features-header-content {
    top: 15px;
    left: 15px;
    gap: 0.3rem;
    padding-left: 10px;
    padding-top: 8px;
  }

  .key-features-header-content .primary.sm {
    font-size: 0.7rem;
    letter-spacing: 0.4px;
  }

  .key-features-header-content2 {
    top: 15px;
    right: 15px;
    gap: 0.3rem;
    padding-right: 10px;
    padding-top: 8px;
  }

  .key-features-header-content2 .primary.sm {
    font-size: 0.7rem;
    letter-spacing: 0.4px;
  }

  .key-features-footer-content {
    bottom: 15px;
    left: 15px;
    gap: 0.3rem;
    padding-left: 10px;
    padding-bottom: 8px;
  }

  .key-features-footer-content .primary.sm {
    font-size: 0.7rem;
    letter-spacing: 0.4px;
  }

  .key-features-footer-content2 {
    bottom: 15px;
    right: 15px;
    gap: 0.3rem;
    padding-right: 10px;
    padding-bottom: 8px;
  }

  .key-features-footer-content2 .primary.sm {
    font-size: 0.7rem;
    letter-spacing: 0.4px;
  }

  /* Make arrow lines visible and responsive on tablets */
  .key-features-header-content::after {
    width: calc(100vw - 95% - 280px);
    height: 1.5px;
    left: calc(100% + 15px);
  }

  .key-features-header-content::before {
    left: calc(100vw - 95% - 150px);
    border-left: 12px solid #242424;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
  }

  .key-features-footer-content::after {
    width: calc(100vw - 95% - 280px);
    height: 1.5px;
    left: calc(100% + 15px);
  }

  .key-features-footer-content::before {
    left: calc(100vw - 95% - 130px);
    border-left: 12px solid #242424;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
  }
}

@media (max-width: 480px) {
  .key-features-header-content {
    top: 10px;
    left: 10px;
    gap: 0.2rem;
    padding-left: 15px;
    padding-top: 12px;
  }

  .key-features-header-content .primary.sm {
    font-size: 0.6rem;
    letter-spacing: 0.2px;
  }

  .key-features-header-content2 {
    top: 10px;
    right: 10px;
    gap: 0.2rem;
    padding-right: 15px;
    padding-top: 12px;
  }

  .key-features-header-content2 .primary.sm {
    font-size: 0.6rem;
    letter-spacing: 0.2px;
  }

  .key-features-footer-content {
    bottom: 10px;
    left: 10px;
    gap: 0.2rem;
    padding-left: 15px;
    padding-bottom: 12px;
  }

  .key-features-footer-content .primary.sm {
    font-size: 0.6rem;
    letter-spacing: 0.2px;
  }

  .key-features-footer-content2 {
    bottom: 10px;
    right: 10px;
    gap: 0.2rem;
    padding-right: 15px;
    padding-bottom: 12px;
  }

  .key-features-footer-content2 .primary.sm {
    font-size: 0.6rem;
    letter-spacing: 0.2px;
  }

  /* Make arrow lines even smaller on mobile */
  .key-features-header-content::after {
    width: calc(100vw - 95% - 200px);
    height: 1px;
    left: calc(100% + 10px);
    top: 60%;
  }

  .key-features-header-content::before {
    left: calc(100vw - 95% - 73px);
    border-left: 10px solid #242424;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    top: 60%;

  }

  .key-features-footer-content::after {
    width: calc(100vw - 95% - 200px);
    height: 1px;
    left: calc(100% + 10px);
    top: 35%;
  }

  .key-features-footer-content::before {
    left: calc(100vw - 95% - 65px);
    border-left: 10px solid #242424;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    top: 35%;
  }

  .key-features-header h1 {
    font-size: 2.5rem;
  }

  .process-card {
    padding: 0.6rem;
    gap: 0.6rem;
  }

  .process-card-index h1 {
    font-size: 2rem;
  }

  .process-card-header {
    font-size: clamp(1.1rem, 1.8vw, 1.5rem);
    margin-bottom: 0.2rem;
  }

  .process-card-subtitle p {
    font-size: clamp(0.7rem, 0.9vw, 0.85rem);
  }

  .process-card-img {
    max-width: 280px;
    height: 130px;
  }

  .features-list {
    gap: 0.5rem;
  }

  .feature-item p {
    font-size: clamp(0.65rem, 0.8vw, 0.75rem);
    line-height: 1.2;
  }

  .process-card-impact {
    margin-top: 0.6rem;
    padding-top: 0.6rem;
  }

  .impact-label .caps {
    font-size: 0.6rem;
  }

  .impact-description p {
    font-size: clamp(0.7rem, 0.85vw, 0.8rem);
  }
}
"use client";
import "./ProcessCards.css";

import { useGSAP } from "@gsap/react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useRef, useState } from "react";
import Copy from "../../Copy/Copy";

gsap.registerPlugin(ScrollTrigger);

const ProcessCards = () => {
  const containerRef = useRef(null);
  const [videoErrors, setVideoErrors] = useState({});

  const handleVideoError = (index) => {
    setVideoErrors(prev => ({
      ...prev,
      [index]: true
    }));
  };

  const processCardsData = [
    {
      index: "01",
      title: "Receptionist Automation",
      video: "/Solution/s1.mp4",
      image: "/Solution/img5.png",
      subtitle: "Streamline front-desk operations with intelligent AI agents that enhance patient interactions and reduce staff workload.",
      features: [
        "AI Calling Agents & Chatbots: Handle patient queries, schedule appointments, and provide doctor recommendations in real-time.",
        "Smart Data Collection: Automatically collect patient information (name, age, symptoms) and auto-fill prescriptions.",
        "Queue Management: Real-time booking, check-in automation, and queue updates via screens, voice boxes, or mobile phones."
      ],
      impact: "Reduces receptionist workload by 50% and improves patient onboarding efficiency."
    },
    {
      index: "02",
      title: "Doctor EMR Assistant",
      video: "/Solution/s3.mp4",
      image: "/Solution/img3.png",
      subtitle: "Empower doctors with an intuitive, AI-assisted Electronic Medical Record (EMR) system that simplifies documentation and enhances clinical workflows.",
      features: [
        "Pen-Paper EMR: Capture handwritten notes on any paper or surface using AI-powered camera pens, instantly converting them to digital records.",
        "Voice-to-Text Notes: Enable doctors to dictate notes for faster, hands-free documentation.",
        "E-Prescription Generation: Auto-generate and share digital prescriptions linked to patient IDs, with access to past visit history."
      ],
      impact: "Saves doctors 30% of their time on paperwork, allowing more focus on patient care."
    },
    {
      index: "03",
      title: "Lab & Pharmacy Automation",
      video: "/Solution/s1.mp4",
      image: "/Solution/img6.png",
      subtitle: "Optimize lab and pharmacy operations with AI-driven workflows that ensure speed, accuracy, and inventory efficiency.",
      features: [
        "AI-Generated Lab Reports: Automate report generation and routing to patients and doctors.",
        "Prescription Routing: Auto-forward prescriptions to pharmacies with real-time inventory checks and alerts.",
        "Digital Prescriptions: Provide QR-code-enabled e-prescriptions for seamless access."
      ],
      impact: "Reduces prescription errors by 40% and speeds up lab/pharmacy workflows."
    },
    {
      index: "04",
      title: "Billing & Payment Automation",
      video: "/Solution/s1.mp4",
      image: "/Solution/img5.png",
      subtitle: "Simplify financial processes with instant billing and payment solutions for OPD and IPD services.",
      features: [
        "Instant Bill Generation: Create bills for consultations, labs, and inpatient services in real-time.",
        "Payment Links: Send UPI or payment links via WhatsApp/SMS for hassle-free transactions.",
        "Insurance Integration: Automate real-time insurance claim checks and discharge billing."
      ],
      impact: "Streamlines billing, reduces delays, and ensures 100% accuracy in financial transactions."
    },
    {
      index: "05",
      title: "IPD Monitoring & Care Coordination",
      video: "/Solution/s3.mp4",
      image: "/Solution/img3.png",
      subtitle: "Enhance inpatient care with AI-driven monitoring and task management for seamless coordination and patient safety.",
      features: [
        "IoT-Based Vitals Tracking: Monitor patient vitals in real-time with AI-generated alerts for anomalies.",
        "Smart Task Management: Auto-remind nurses and doctors of care tasks, rounds, and handovers.",
        "Automated Charting: Generate daily care notes and discharge summaries effortlessly."
      ],
      impact: "Improves care coordination by 35% and reduces errors in inpatient monitoring."
    },
    {
      index: "06",
      title: "Follow-Up & Patient Engagement",
      video: "/Solution/s1.mp4",
      image: "/Solution/img6.png",
      subtitle: "Ensure continuous patient care with automated follow-up and engagement tools.",
      features: [
        "AI Reminder System: Send follow-up calls or messages for appointments and consultations.",
        "Report Sharing: Auto-deliver lab reports and health updates to patients via WhatsApp/SMS.",
        "Feedback Collection: Gather patient feedback and track health outcomes for continuous improvement."
      ],
      impact: "Increases follow-up compliance by 25% and enhances patient satisfaction."
    },
    {
      index: "07",
      title: "Central Intelligence Dashboard",
      video: "/Solution/s3.mp4",
      image: "/Solution/img5.png",
      subtitle: "Gain real-time insights and control over all hospital operations with a powerful AI-driven dashboard.",
      features: [
        "Real-Time Overview: Monitor key performance indicators, patient flow, and operational metrics.",
        "Resource Optimization: Track doctor and patient flow to allocate resources efficiently.",
        "Financial Insights: Access revenue, billing, and insurance data for better financial planning.",
        "Proactive Alerts: Receive notifications for inefficiencies or potential issues.",
        "Training Support: Enhance doctor-patient communication with AI-supported training tools."
      ],
      impact: "Provides comprehensive operational control and data-driven decision making capabilities."
    }
  ];

  useGSAP(() => {
    // Only create ScrollTriggers when the container is in view
    if (!containerRef.current) return;

    // Scope queries to this component's container
    const processCards = containerRef.current.querySelectorAll(".process-card");

    // Add a delay to ensure ProcessCards only animate after ProblemSol is done
    const initScrollTrigger = ScrollTrigger.create({
      trigger: containerRef.current,
      start: "top bottom",
      once: true,
      onEnter: () => {
        processCards.forEach((card, index) => {
          if (index < processCards.length - 1) {
            ScrollTrigger.create({
              trigger: card,
              start: "top top",
              endTrigger: processCards[processCards.length - 1],
              end: "top top",
              pin: true,
              pinSpacing: false,
              id: `card-pin-${index}`,
            });
          }

          if (index < processCards.length - 1) {
            ScrollTrigger.create({
              trigger: processCards[index + 1],
              start: "top bottom",
              end: "top top",
              onUpdate: (self) => {
                const progress = self.progress;
                const scale = 1 - progress * 0.25;
                const rotation = (index % 2 === 0 ? 5 : -5) * progress;
                const afterOpacity = progress;

                gsap.set(card, {
                  scale: scale,
                  rotation: rotation,
                  "--after-opacity": afterOpacity,
                });
              },
            });
          }
        });
      }
    });

    return () => {
      if (initScrollTrigger) initScrollTrigger.kill();
    };
  }, []);

  return (
    <>
      {/* Key Features Header Section */}
      <section className="key-features-header">
        <div className="key-features-container">
          <div className="key-features-header-content">
            <Copy animateOnScroll={true}>
              <p className="primary sm">Advanced Healthcare</p>
              <p className="primary sm">Management System</p>
            </Copy>
          </div>

          <div className="key-features-header-content2">
            <Copy animateOnScroll={true}>
              <p className="primary sm">Intelligent Solutions</p>
              <p className="primary sm">for Modern Healthcare</p>
            </Copy>
          </div>

          <Copy animateOnScroll={true}>
            <h1>KEY FEATURES</h1>
            <h1>OF AIR-HS</h1>
          </Copy>

          <div className="key-features-footer-content">
            <Copy animateOnScroll={true}>
              <p className="primary sm">Streamlined Operations</p>
              <p className="primary sm">Enhanced Patient Care</p>
            </Copy>
          </div>

          <div className="key-features-footer-content2">
            <Copy animateOnScroll={true}>
              <p className="primary sm">Digital Transformation</p>
              <p className="primary sm">Healthcare Innovation</p>
            </Copy>
          </div>
        </div>
      </section>

      {/* Process Cards Section */}
      <div ref={containerRef} className="process-cards">
        {processCardsData.map((cardData, index) => (
          <div key={index} className="process-card">
            <div className="process-card-index">
              <h1>{cardData.index}</h1>
            </div>
            <div className="process-card-content">
              <div className="process-card-content-wrapper">
                <h1 className="process-card-header">{cardData.title}</h1>

                <div className="process-card-subtitle">
                  <p>{cardData.subtitle}</p>
                </div>

                <div className="process-card-img">
                  {cardData.video && !videoErrors[index] ? (
                    <video
                      src={cardData.video}
                      autoPlay
                      loop
                      muted
                      playsInline
                      onError={() => handleVideoError(index)}
                      style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                    />
                  ) : (
                    <img src={cardData.image} alt="" />
                  )}
                </div>

                <div className="process-card-features">
                  <div className="features-list">
                    {cardData.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="feature-item">
                        <p>• {feature}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="process-card-impact">
                  <div className="impact-label">
                    <p className="caps">(Impact)</p>
                  </div>
                  <div className="impact-description">
                    <p>{cardData.impact}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default ProcessCards;
